diff --git a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
index 5529cd649..202b35f6f 100644
--- a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
+++ b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
@@ -18,6 +18,7 @@ interface ShipTableRatesInterface
     const WEIGHT = 'weight';
     const SHIPPING_PRICE = 'shipping_price';
     const FREE_SHIPPING = 'free_shipping';
+    const MIN_ORDER_AMOUNT = 'min_order_amount';
 
     /**
      * Get shiptablerates_id
@@ -175,5 +176,18 @@ interface ShipTableRatesInterface
      */
     public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface;
 
+    /**
+     * Get minimum order amount
+     * @return float|null
+     */
+    public function getMinOrderAmount(): ?float;
+
+    /**
+     * Set minimum order amount
+     * @param float $minOrderAmount
+     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface;
+
 }
 
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
index e3f223dd4..e9e6685ee 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
@@ -10,7 +10,7 @@
  */
 namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 
-class Manage extends \Webkul\MpSellerCategory\Controller\AbstractCategory
+class Manage extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
      * Execute Method
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
index a848ecfba..05161991b 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
@@ -51,12 +51,24 @@ class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 
                 $sellerShiprate->save();
                 $id = $sellerShiprate->getShiptableratesId();
-                $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
-                $this->_helper->clearCache();
-                return $this->resultRedirectFactory->create()->setPath(
-                    'coditron_customshippingrate/shiptablerates/edit',
-                    ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
-                );
+
+                // Check if this is a threshold save
+                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+                if ($isThreshold) {
+                    $this->messageManager->addSuccess(__("Free Shipping Threshold saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/editthreshold',
+                        ['id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                } else {
+                    $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/edit',
+                        ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                }
             } catch (\Exception $e) {
                 $this->messageManager->addError($e->getMessage());
                 return $this->resultRedirectFactory->create()->setPath(
diff --git a/app/code/Coditron/CustomShippingRate/Model/Carrier.php b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
index 8d1ad90e1..344c44b63 100644
--- a/app/code/Coditron/CustomShippingRate/Model/Carrier.php
+++ b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
@@ -158,6 +158,11 @@ class Carrier extends AbstractCarrier implements CarrierInterface
                     continue;
                 }
 
+                // Skip free shipping thresholds from regular rates
+                if ($tableRate->getMinAmount() && $tableRate->getMinAmount() > 0) {
+                    continue;
+                }
+
                 [, $sellerId] = explode('|', $sellerKey);
                 $totalPrice += $tableRate->getShippingPrice();
                 $minLeadTime = $minLeadTime === null ? $tableRate->getTotalLeadTime() : min($minLeadTime, $tableRate->getTotalLeadTime());
diff --git a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
index 5369590c3..7c24e25d2 100644
--- a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
+++ b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
@@ -215,6 +215,22 @@ class ShipTableRates extends AbstractModel implements ShipTableRatesInterface
         return $this->setData(self::FREE_SHIPPING, $freeShipping);
     }
 
+    /**
+     * @inheritDoc
+     */
+    public function getMinOrderAmount(): ?float
+    {
+        return (float)$this->getData(self::MIN_ORDER_AMOUNT);
+    }
+
+    /**
+     * @inheritDoc
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface
+    {
+        return $this->setData(self::MIN_ORDER_AMOUNT, $minOrderAmount);
+    }
+
     /**
      * Override to Converts countries array to string
      * @param $key
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
index 4043e1b79..0c3b4c2e7 100755
--- a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
@@ -15,6 +15,7 @@ use Magento\Quote\Model\Quote\Address\Total;
 use Magento\Quote\Model\Quote\Address\Total\Shipping;
 use Coditron\CustomShippingRate\Helper\Data;
 use Coditron\CustomShippingRate\Model\Carrier;
+use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
 
 class ShippingPlugin
 {
@@ -23,13 +24,21 @@ class ShippingPlugin
      */
     protected $customShippingRateHelper;
 
+    /**
+     * @var ShipTableRatesRepositoryInterface
+     */
+    protected $shipTableRatesRepository;
+
     /**
      * @param Data $customShippingRateHelper
+     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
      */
     public function __construct(
-        Data $customShippingRateHelper
+        Data $customShippingRateHelper,
+        ShipTableRatesRepositoryInterface $shipTableRatesRepository
     ) {
         $this->customShippingRateHelper = $customShippingRateHelper;
+        $this->shipTableRatesRepository = $shipTableRatesRepository;
     }
 
     /**
@@ -54,7 +63,7 @@ class ShippingPlugin
 
         if (!$this->customShippingRateHelper->isEnabled($storeId)
             || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
-            || strpos((string) $method, Carrier::CODE) === false
+            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping') === false)
         ) {
             return $proceed($quote, $shippingAssignment, $total);
         }
@@ -65,7 +74,15 @@ class ShippingPlugin
             //update shipping code
             $shipping->setMethod($customShippingOption['code']);
             $address->setShippingMethod($customShippingOption['code']);
+
             $this->updateCustomRate($address, $customShippingOption);
+
+            // For free shipping methods, ensure totals are set to zero
+            if (strpos($method, 'freeshipping') === 0) {
+                $total->setShippingAmount(0);
+                $total->setBaseShippingAmount(0);
+                $total->setShippingDescription($customShippingOption['description'] ?? 'Free Shipping');
+            }
         }
 
         return $proceed($quote, $shippingAssignment, $total);
@@ -108,11 +125,21 @@ class ShippingPlugin
                 $rate = $selectedRate->getPrice();
             }
 
-            $jsonToArray = [
-                'code' => $json,
-                'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
-                'rate' => $rate
-            ];
+            // Handle free shipping codes
+            if (strpos($json, 'freeshipping') === 0) {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => 'free_shipping',
+                    'rate' => 0, // Free shipping
+                    'description' => 'Free Shipping'
+                ];
+            } else {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
+                    'rate' => $rate
+                ];
+            }
 
             return $this->formatShippingArray($jsonToArray);
         }
@@ -147,6 +174,8 @@ class ShippingPlugin
         return $selectedRate;
     }
 
+
+
     /**
      * @param $jsonToArray array
      * @return array
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
index 12f42277c..b76c8f100 100644
--- a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
@@ -55,6 +55,9 @@ class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractData
         $this->collection->addFieldToFilter(
             'seller_id',
             ['eq' => $this->helper->getSellerId()]
+        )->addFieldToFilter(
+            'min_order_amount',
+            ['eq' => 0]
         );
     }
 }
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
index 68559032e..f1710aec1 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
@@ -21,6 +21,7 @@
         <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
         <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
         <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
+        <column name="min_order_amount" nullable="true" xsi:type="decimal" precision="12" scale="4" default="0.0000" comment="Minimum Order Amount for Free Shipping"/>
 		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
 		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
         <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
index 3d5012378..c9b75af9f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
@@ -22,6 +22,7 @@
             "weight": true,
             "shipping_price": true,
             "free_shipping": true,
+            "min_order_amount": true,
             "seller_id": true
         },
         "index": {
diff --git a/app/code/Coditron/CustomShippingRate/etc/di.xml b/app/code/Coditron/CustomShippingRate/etc/di.xml
index 68c6a903c..96187ef7f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/di.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/di.xml
@@ -6,6 +6,9 @@
 	<type name="Magento\Checkout\Model\ShippingInformationManagement">
 		<plugin name="update_shipping_data_on_method_selection" type="Coditron\CustomShippingRate\Plugin\UpdateShippingDataOnMethodSelection" />
 	</type>
+	<type name="Magento\OfflineShipping\Model\Carrier\Freeshipping">
+		<plugin name="disable_core_freeshipping_when_thresholds_exist" type="Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier\FreeShippingPlugin" />
+	</type>
 	<preference for="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" type="Coditron\CustomShippingRate\Model\CustomShippingInformationManagement" />
 	<preference for="Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface"
                 type="Coditron\CustomShippingRate\Model\Data\CustomShippingInformation" />
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
index 63e68d639..cbab4c091 100755
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
@@ -16,6 +16,7 @@
 $taxHelper = $block->getData('taxHelper');
 ?>
 <?php $_shippingRateGroups = $block->getGroupShippingRates(); ?>
+
 <?php if ($_shippingRateGroups): ?>
     <div id="order-shipping-method-choose" class="control">
         <dl class="admin__order-shipment-methods">
@@ -66,6 +67,9 @@ $taxHelper = $block->getData('taxHelper');
                 </ul>
             </dd>
         <?php endforeach; ?>
+
+        
+
             <?php /*------- Start Coditron --------*/ ?>
             <?php if ($this->helper('Coditron\CustomShippingRate\Helper\Data')->isEnabled($this->getQuote()->getStore()->getStoreId())): ?>
                 <dt class="admin__order-shipment-methods-title">
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml
index 5d24fa8dc..756abb5ca 100644
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml
@@ -107,6 +107,23 @@
 				</validation>
 			</settings>
 		</field>
+		<field name="min_order_amount" formElement="input" sortOrder="45">
+			<argument name="data" xsi:type="array">
+				<item name="config" xsi:type="array">
+					<item name="source" xsi:type="string">ShipTableRates</item>
+					<item name="notice" xsi:type="string" translate="true">Minimum order amount for free shipping (USD)</item>
+				</item>
+			</argument>
+			<settings>
+				<dataType>text</dataType>
+				<label translate="true">Min Order Amount</label>
+				<dataScope>min_order_amount</dataScope>
+				<validation>
+					<rule name="validate-number" xsi:type="boolean">true</rule>
+					<rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
+				</validation>
+			</settings>
+		</field>
 		<field name="seller_id" formElement="input" sortOrder="50">
 			<argument name="data" xsi:type="array">
 				<item name="config" xsi:type="array">
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml
index 81b68846f..b3291de20 100644
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml
@@ -136,6 +136,19 @@
 				</editor>
 			</settings>
 		</column>
+		<column name="min_order_amount">
+			<settings>
+				<filter>text</filter>
+				<label translate="true">Min Order Amount</label>
+				<editor>
+					<editorType>text</editorType>
+					<validation>
+						<rule name="validate-number" xsi:type="boolean">true</rule>
+						<rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
+					</validation>
+				</editor>
+			</settings>
+		</column>
 		<actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ShipTableRatesActions">
 			<settings>
 				<indexField>shiptablerates_id</indexField>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
index 972c401ce..68d255542 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
@@ -7,6 +7,7 @@
         <css src="Webkul_Marketplace::css/style.css"/>
         <css src="Webkul_Marketplace::css/product.css"/>
         <css src="Webkul_Marketplace::css/layout.css"/>
+        <css src="Coditron_CustomShippingRate::css/tabbed-rates.css"/>
 
     </head>
     <body>
@@ -16,10 +17,11 @@
             </action>
         </referenceBlock>
         <referenceContainer name="seller.content">
-            <block class="Magento\Framework\View\Element\Template" name="sellership_rate_manage" template="Coditron_CustomShippingRate::shiprate/list.phtml" cacheable="false"></block>
+            <block class="Coditron\CustomShippingRate\Block\TabbedRates" name="sellership_rate_manage" template="Coditron_CustomShippingRate::shiprate/tabbed_list.phtml" cacheable="false"></block>
         </referenceContainer>
         <referenceContainer name="sellership_rate_manage">
             <uiComponent name="sellership_rates_list_front"/>
+            <uiComponent name="sellership_threshold_list_front"/>
         </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
index 3894ab393..d90854804 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
@@ -1,7 +1,7 @@
 <?php
      $marketplaceHelper = $block->getMpHelper();
      $isPartner= $marketplaceHelper->isSeller();
-     $isEnable = false;
+     $isEnable = true;
      $magentoCurrentUrl = $block->getCurrentUrl();
 ?>
 <?php if ($isPartner): ?>
diff --git a/app/etc/config.php b/app/etc/config.php
index a71a8b83e..f788ea245 100644
--- a/app/etc/config.php
+++ b/app/etc/config.php
@@ -783,6 +783,7 @@ return [
         'Comave_EmailConfig' => 1,
         'Comave_EnhancedReviewGraphql' => 1,
         'Comave_ExperienceGraphQl' => 1,
+        'Webkul_MpSellerCategory' => 1,
         'Comave_GraphQLMiddleware' => 1,
         'Comave_Integration' => 1,
         'Comave_Marketplace' => 1,
@@ -818,7 +819,7 @@ return [
         'Comave_ShopifyAccounts' => 1,
         'Comave_BigBuy' => 1,
         'Comave_SellerPayouts' => 1,
-        'Webkul_MpSellerCategory' => 1,
+        'Coditron_CustomShippingRate' => 1,
         'Comave_SellerReport' => 1,
         'Comave_SellerRequest' => 0,
         'Comave_SellerShippingCountry' => 1,
@@ -828,7 +829,7 @@ return [
         'Webkul_MpMultiShopifyStoreMageConnectExtend' => 1,
         'Comave_Soccer' => 1,
         'Comave_SoccerGraphQl' => 1,
-        'Coditron_CustomShippingRate' => 1,
+        'Comave_CatalogGraphQl' => 1,
         'Comave_Sportsclub' => 0,
         'Comave_SportsclubFan' => 1,
         'Comave_StoreConfig' => 1,
@@ -866,7 +867,7 @@ return [
         'Coditron_Mpmultistorewoocommerce' => 0,
         'Webkul_MarketplaceBaseShipping' => 1,
         'Webkul_MarketplacePreorder' => 1,
-        'Comave_CatalogGraphQl' => 1,
+        'Comave_RmaGraphQl' => 1,
         'Comave_CustomerGraphQl' => 1,
         'Comave_ProductOffers' => 1,
         'Comave_TrackingStatus' => 0,
@@ -876,9 +877,8 @@ return [
         'Comave_CustomerIoEmail' => 1,
         'Comave_SellerProductShippingCost' => 1,
         'Webkul_MpSellerCoupons' => 1,
-        'Comave_SplitOrder' => 1,
-        'Webkul_MpVendorAttributeManager' => 1,
         'Comave_FreeShippingTreshold' => 1,
+        'Webkul_MpVendorAttributeManager' => 1,
         'Webkul_Mppercountryperproductshipping' => 1,
         'Comave_OutOfStockNotification' => 1,
         'Webkul_PriceDropAlert' => 1,
@@ -898,7 +898,7 @@ return [
         'WeltPixel_LazyLoading' => 1,
         'WeltPixel_MobileDetect' => 1,
         'WeltPixel_Multistore' => 1,
-        'Comave_RmaGraphQl' => 1,
+        'Comave_SplitOrder' => 1,
         'WeltPixel_ProductPage' => 0,
         'WeltPixel_QuickCart' => 1,
         'WeltPixel_ReviewsWidget' => 1,
diff --git a/composer.lock b/composer.lock
index 2407f8241..75e955b80 100644
--- a/composer.lock
+++ b/composer.lock
@@ -4,7 +4,7 @@
         "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies",
         "This file is @generated automatically"
     ],
-    "content-hash": "b5bdf369488abf4630e4aa0a702c0f83",
+    "content-hash": "6db1995670e437bdbb813637887f19d7",
     "packages": [
         {
             "name": "2tvenom/cborencode",
@@ -204,16 +204,16 @@
         },
         {
             "name": "aws/aws-sdk-php",
-            "version": "3.343.15",
+            "version": "3.343.20",
             "source": {
                 "type": "git",
                 "url": "https://github.com/aws/aws-sdk-php.git",
-                "reference": "554d983fbe1a9fd388d0e916cbeec2a5c48d58ef"
+                "reference": "bf40b00d2e1cbd2a5d8c903743073440d8ebb5dc"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/554d983fbe1a9fd388d0e916cbeec2a5c48d58ef",
-                "reference": "554d983fbe1a9fd388d0e916cbeec2a5c48d58ef",
+                "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/bf40b00d2e1cbd2a5d8c903743073440d8ebb5dc",
+                "reference": "bf40b00d2e1cbd2a5d8c903743073440d8ebb5dc",
                 "shasum": ""
             },
             "require": {
@@ -295,9 +295,9 @@
             "support": {
                 "forum": "https://github.com/aws/aws-sdk-php/discussions",
                 "issues": "https://github.com/aws/aws-sdk-php/issues",
-                "source": "https://github.com/aws/aws-sdk-php/tree/3.343.15"
+                "source": "https://github.com/aws/aws-sdk-php/tree/3.343.20"
             },
-            "time": "2025-05-20T18:05:45+00:00"
+            "time": "2025-05-28T18:10:03+00:00"
         },
         {
             "name": "bacon/bacon-qr-code",
@@ -773,16 +773,16 @@
         },
         {
             "name": "composer/ca-bundle",
-            "version": "1.5.6",
+            "version": "1.5.7",
             "source": {
                 "type": "git",
                 "url": "https://github.com/composer/ca-bundle.git",
-                "reference": "f65c239c970e7f072f067ab78646e9f0b2935175"
+                "reference": "d665d22c417056996c59019579f1967dfe5c1e82"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/composer/ca-bundle/zipball/f65c239c970e7f072f067ab78646e9f0b2935175",
-                "reference": "f65c239c970e7f072f067ab78646e9f0b2935175",
+                "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d665d22c417056996c59019579f1967dfe5c1e82",
+                "reference": "d665d22c417056996c59019579f1967dfe5c1e82",
                 "shasum": ""
             },
             "require": {
@@ -829,7 +829,7 @@
             "support": {
                 "irc": "irc://irc.freenode.org/composer",
                 "issues": "https://github.com/composer/ca-bundle/issues",
-                "source": "https://github.com/composer/ca-bundle/tree/1.5.6"
+                "source": "https://github.com/composer/ca-bundle/tree/1.5.7"
             },
             "funding": [
                 {
@@ -845,7 +845,7 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-03-06T14:30:56+00:00"
+            "time": "2025-05-26T15:08:54+00:00"
         },
         {
             "name": "composer/class-map-generator",
@@ -1791,21 +1791,21 @@
         },
         {
             "name": "diablomedia/zendframework1-filter",
-            "version": "1.0.8",
+            "version": "1.0.9",
             "source": {
                 "type": "git",
                 "url": "https://github.com/diablomedia/zf1-filter.git",
-                "reference": "7b51a99f30e080bdcd21889ccd8466c4b318f25c"
+                "reference": "a369f424f6d6a127f9669c612fdec0bf08234a77"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/diablomedia/zf1-filter/zipball/7b51a99f30e080bdcd21889ccd8466c4b318f25c",
-                "reference": "7b51a99f30e080bdcd21889ccd8466c4b318f25c",
+                "url": "https://api.github.com/repos/diablomedia/zf1-filter/zipball/a369f424f6d6a127f9669c612fdec0bf08234a77",
+                "reference": "a369f424f6d6a127f9669c612fdec0bf08234a77",
                 "shasum": ""
             },
             "require": {
                 "diablomedia/zendframework1-config": "^2.0.5",
-                "diablomedia/zendframework1-crypt": "^1.0.5",
+                "diablomedia/zendframework1-crypt": "^1.0.8",
                 "diablomedia/zendframework1-date": "^1.0.5",
                 "diablomedia/zendframework1-exception": "^1.1.1",
                 "diablomedia/zendframework1-loader": "^1.0.5",
@@ -1813,11 +1813,11 @@
                 "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"
             },
             "require-dev": {
-                "friendsofphp/php-cs-fixer": "3.67.0",
+                "friendsofphp/php-cs-fixer": "3.75.0",
                 "maglnet/composer-require-checker": "^3.0.0",
                 "pear/archive_tar": "^1.4.6",
                 "phpro/grumphp-shim": "^2.0.0",
-                "phpstan/phpstan": "2.1.1",
+                "phpstan/phpstan": "2.1.17",
                 "phpunit/phpunit": "^9.6.19 || ^10.5.18"
             },
             "suggest": {
@@ -1858,9 +1858,9 @@
             ],
             "support": {
                 "issues": "https://github.com/diablomedia/zf1-filter/issues",
-                "source": "https://github.com/diablomedia/zf1-filter/tree/1.0.8"
+                "source": "https://github.com/diablomedia/zf1-filter/tree/1.0.9"
             },
-            "time": "2025-01-10T19:39:28+00:00"
+            "time": "2025-05-22T16:19:59+00:00"
         },
         {
             "name": "diablomedia/zendframework1-filter-input",
@@ -4556,16 +4556,16 @@
         },
         {
             "name": "laminas/laminas-inputfilter",
-            "version": "2.32.0",
+            "version": "2.33.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/laminas/laminas-inputfilter.git",
-                "reference": "5f04f1e5a6b1bd3c82114b6ea748a70ce65787f6"
+                "reference": "928afe6f5e7c7a17f9c02c40d4feca92944d8e2f"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/laminas/laminas-inputfilter/zipball/5f04f1e5a6b1bd3c82114b6ea748a70ce65787f6",
-                "reference": "5f04f1e5a6b1bd3c82114b6ea748a70ce65787f6",
+                "url": "https://api.github.com/repos/laminas/laminas-inputfilter/zipball/928afe6f5e7c7a17f9c02c40d4feca92944d8e2f",
+                "reference": "928afe6f5e7c7a17f9c02c40d4feca92944d8e2f",
                 "shasum": ""
             },
             "require": {
@@ -4573,18 +4573,19 @@
                 "laminas/laminas-servicemanager": "^3.21.0",
                 "laminas/laminas-stdlib": "^3.19",
                 "laminas/laminas-validator": "^2.60.0",
-                "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"
+                "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0",
+                "psr/container": "^1.1 || ^2.0"
             },
             "conflict": {
                 "zendframework/zend-inputfilter": "*"
             },
             "require-dev": {
                 "ext-json": "*",
-                "laminas/laminas-coding-standard": "^3.0.1",
-                "phpunit/phpunit": "^10.5.45",
+                "laminas/laminas-coding-standard": "^3.1.0",
+                "phpunit/phpunit": "^10.5.46",
                 "psalm/plugin-phpunit": "^0.19.5",
                 "psr/http-message": "^2.0",
-                "vimeo/psalm": "^6.10.0",
+                "vimeo/psalm": "^6.11.0",
                 "webmozart/assert": "^1.11"
             },
             "suggest": {
@@ -4626,7 +4627,7 @@
                     "type": "community_bridge"
                 }
             ],
-            "time": "2025-04-14T00:23:41+00:00"
+            "time": "2025-05-27T09:48:19+00:00"
         },
         {
             "name": "laminas/laminas-json",
@@ -9944,11 +9945,11 @@
         },
         {
             "name": "magento/module-bundle-product-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-bundle-product-data-exporter/magento-module-bundle-product-data-exporter-*********.zip",
-                "shasum": "10d1cccb988541a7619535228790df5c7b184fe1"
+                "url": "https://repo.magento.com/archives/magento/module-bundle-product-data-exporter/magento-module-bundle-product-data-exporter-*********.zip",
+                "shasum": "4c0b026bf647234ea769e184279bb768edb8edcf"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -9979,11 +9980,11 @@
         },
         {
             "name": "magento/module-bundle-product-override-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-bundle-product-override-data-exporter/magento-module-bundle-product-override-data-exporter-*********.zip",
-                "shasum": "027e8c30af3baa591a8b1813d709551e21778081"
+                "url": "https://repo.magento.com/archives/magento/module-bundle-product-override-data-exporter/magento-module-bundle-product-override-data-exporter-*********.zip",
+                "shasum": "828a3e74925affa6a70aaa9e88454537943627a0"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -10283,11 +10284,11 @@
         },
         {
             "name": "magento/module-catalog-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-catalog-data-exporter/magento-module-catalog-data-exporter-*********.zip",
-                "shasum": "e1559c4b6029358cb147b84f557aed12aeb53453"
+                "url": "https://repo.magento.com/archives/magento/module-catalog-data-exporter/magento-module-catalog-data-exporter-*********.zip",
+                "shasum": "a03b160446c4a170678cd7f53ad34f1498248752"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -10509,11 +10510,11 @@
         },
         {
             "name": "magento/module-catalog-inventory-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-catalog-inventory-data-exporter/magento-module-catalog-inventory-data-exporter-*********.zip",
-                "shasum": "9d3d70f5e447123ca44081b05dd2df1c41f7ea73"
+                "url": "https://repo.magento.com/archives/magento/module-catalog-inventory-data-exporter/magento-module-catalog-inventory-data-exporter-*********.zip",
+                "shasum": "1e4ba70223c0c5e7420c8114164595fe0b58cebe"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -11149,11 +11150,11 @@
         },
         {
             "name": "magento/module-catalog-url-rewrite-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-catalog-url-rewrite-data-exporter/magento-module-catalog-url-rewrite-data-exporter-*********.zip",
-                "shasum": "e399f6894e778d192f1b2dfa2c35164f04c9c66b"
+                "url": "https://repo.magento.com/archives/magento/module-catalog-url-rewrite-data-exporter/magento-module-catalog-url-rewrite-data-exporter-*********.zip",
+                "shasum": "ffe31e220f8050e7c2a72ffa93693162aed98767"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -11898,11 +11899,11 @@
         },
         {
             "name": "magento/module-configurable-product-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-configurable-product-data-exporter/magento-module-configurable-product-data-exporter-*********.zip",
-                "shasum": "ed150b36c6bb24414ffe7d2a6ff5d1e652787fae"
+                "url": "https://repo.magento.com/archives/magento/module-configurable-product-data-exporter/magento-module-configurable-product-data-exporter-*********.zip",
+                "shasum": "959338f282d7efd716a0986abe1e4691dc809bb0"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -12683,11 +12684,11 @@
         },
         {
             "name": "magento/module-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-data-exporter/magento-module-data-exporter-*********.zip",
-                "shasum": "b8d587b19aec494c477fd9993c12cf4bae0c49cc"
+                "url": "https://repo.magento.com/archives/magento/module-data-exporter/magento-module-data-exporter-*********.zip",
+                "shasum": "7106cbd6fa8e26ed0416533cafd673394db22129"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -13671,17 +13672,18 @@
         },
         {
             "name": "magento/module-gift-card-product-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-gift-card-product-data-exporter/magento-module-gift-card-product-data-exporter-*********.zip",
-                "shasum": "c766f92beeec6b8876bbd5cc6a1e523eb5fcf173"
+                "url": "https://repo.magento.com/archives/magento/module-gift-card-product-data-exporter/magento-module-gift-card-product-data-exporter-*********.zip",
+                "shasum": "5f426348e383f21ec2f98f0ea69a52e76871af6c"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
                 "magento/module-catalog": ">=104.0.4",
                 "magento/module-catalog-data-exporter": "self.version",
                 "magento/module-data-exporter": "self.version",
+                "magento/module-eav": ">=102.1.4",
                 "magento/module-gift-card": ">=101.3.4",
                 "magento/module-store": ">=101.1.4",
                 "php": "~8.1.0||~8.2.0||~8.3.0||~8.4.0"
@@ -18986,11 +18988,11 @@
         },
         {
             "name": "magento/module-parent-product-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-parent-product-data-exporter/magento-module-parent-product-data-exporter-*********.zip",
-                "shasum": "1334241d0b8e409ad1118da1ce5a520ca042f736"
+                "url": "https://repo.magento.com/archives/magento/module-parent-product-data-exporter/magento-module-parent-product-data-exporter-*********.zip",
+                "shasum": "7fc54a387d4c234bc637e697b0667a8b8301248c"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -19576,11 +19578,11 @@
         },
         {
             "name": "magento/module-product-override-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-product-override-data-exporter/magento-module-product-override-data-exporter-*********.zip",
-                "shasum": "b13052edf7646533385d9cc8b375ed07404763e2"
+                "url": "https://repo.magento.com/archives/magento/module-product-override-data-exporter/magento-module-product-override-data-exporter-*********.zip",
+                "shasum": "de5bac705c5dff3762c896b55e05005e37514de4"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -19616,11 +19618,11 @@
         },
         {
             "name": "magento/module-product-price-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-product-price-data-exporter/magento-module-product-price-data-exporter-*********.zip",
-                "shasum": "7ebf4374844b15e44d1e259a08ed0730f61cd991"
+                "url": "https://repo.magento.com/archives/magento/module-product-price-data-exporter/magento-module-product-price-data-exporter-*********.zip",
+                "shasum": "48fcd4028d0e2fb5f51b6055f8074367b0b91110"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -19822,11 +19824,11 @@
         },
         {
             "name": "magento/module-query-xml",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-query-xml/magento-module-query-xml-*********.zip",
-                "shasum": "bb0ff3d8d3ce745f5154476a4f83437669873dce"
+                "url": "https://repo.magento.com/archives/magento/module-query-xml/magento-module-query-xml-*********.zip",
+                "shasum": "3ac8f1f498e9ef7fd3515b72459a8f74c314cdfc"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -21689,11 +21691,11 @@
         },
         {
             "name": "magento/module-saas-catalog",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-saas-catalog/magento-module-saas-catalog-*********.zip",
-                "shasum": "84eab84389247fdd514bd94a7f2bfad96c95a7ad"
+                "url": "https://repo.magento.com/archives/magento/module-saas-catalog/magento-module-saas-catalog-*********.zip",
+                "shasum": "162cd0409dfc4e22fac6e4068ddd8f5552a22028"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -21722,11 +21724,11 @@
         },
         {
             "name": "magento/module-saas-category",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-saas-category/magento-module-saas-category-*********.zip",
-                "shasum": "4dc39faab1ab98a7f674d9eb8d74f573d198e754"
+                "url": "https://repo.magento.com/archives/magento/module-saas-category/magento-module-saas-category-*********.zip",
+                "shasum": "049bb5295a814b1113a84387bab6ee455c5d13b1"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -21755,11 +21757,11 @@
         },
         {
             "name": "magento/module-saas-common",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-saas-common/magento-module-saas-common-*********.zip",
-                "shasum": "ba6a1abc56b413d91dedf69e23c98a28581fea29"
+                "url": "https://repo.magento.com/archives/magento/module-saas-common/magento-module-saas-common-*********.zip",
+                "shasum": "3e5f7bb97b258ec6f1b1b063a1d33c48878584bb"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -21792,11 +21794,11 @@
         },
         {
             "name": "magento/module-saas-price",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-saas-price/magento-module-saas-price-*********.zip",
-                "shasum": "e433efb23e20e24abc31f2b8829053a75562aaf6"
+                "url": "https://repo.magento.com/archives/magento/module-saas-price/magento-module-saas-price-*********.zip",
+                "shasum": "b5d9d419f805e80390d63a0c6ff66a5379f4fe3f"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -21825,11 +21827,11 @@
         },
         {
             "name": "magento/module-saas-product-override",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-saas-product-override/magento-module-saas-product-override-*********.zip",
-                "shasum": "372045adab559b15cf64dfa593697bc635d49212"
+                "url": "https://repo.magento.com/archives/magento/module-saas-product-override/magento-module-saas-product-override-*********.zip",
+                "shasum": "14ba91b0bb878355a73f9d2a4f52d94d655f4548"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -21858,11 +21860,11 @@
         },
         {
             "name": "magento/module-saas-scopes",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-saas-scopes/magento-module-saas-scopes-*********.zip",
-                "shasum": "0bfac21a9762e6708bc02753e36b102826720dce"
+                "url": "https://repo.magento.com/archives/magento/module-saas-scopes/magento-module-saas-scopes-*********.zip",
+                "shasum": "177b5dd3ac064d6948423480dfe80a5c0e67458f"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -22403,11 +22405,11 @@
         },
         {
             "name": "magento/module-scopes-data-exporter",
-            "version": "103.4.4",
+            "version": "103.4.5",
             "dist": {
                 "type": "zip",
-                "url": "https://repo.magento.com/archives/magento/module-scopes-data-exporter/magento-module-scopes-data-exporter-*********.zip",
-                "shasum": "8a4e15d6448b724550a4fa32056be362eb6f40e9"
+                "url": "https://repo.magento.com/archives/magento/module-scopes-data-exporter/magento-module-scopes-data-exporter-*********.zip",
+                "shasum": "fd084215b1e1cf413219dfe0f6cb224fbc721b0c"
             },
             "require": {
                 "magento/framework": ">=103.0.4",
@@ -28711,16 +28713,16 @@
         },
         {
             "name": "symfony/config",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/config.git",
-                "reference": "e0b050b83ba999aa77a3736cb6d5b206d65b9d0d"
+                "reference": "ba62ae565f1327c2f6366726312ed828c85853bc"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/config/zipball/e0b050b83ba999aa77a3736cb6d5b206d65b9d0d",
-                "reference": "e0b050b83ba999aa77a3736cb6d5b206d65b9d0d",
+                "url": "https://api.github.com/repos/symfony/config/zipball/ba62ae565f1327c2f6366726312ed828c85853bc",
+                "reference": "ba62ae565f1327c2f6366726312ed828c85853bc",
                 "shasum": ""
             },
             "require": {
@@ -28766,7 +28768,7 @@
             "description": "Helps you find, load, combine, autofill and validate configuration values of any kind",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/config/tree/v7.2.6"
+                "source": "https://github.com/symfony/config/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -28782,20 +28784,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-03T21:14:15+00:00"
+            "time": "2025-05-15T09:04:05+00:00"
         },
         {
             "name": "symfony/console",
-            "version": "v6.4.21",
+            "version": "v6.4.22",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/console.git",
-                "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719"
+                "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/console/zipball/a3011c7b7adb58d89f6c0d822abb641d7a5f9719",
-                "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719",
+                "url": "https://api.github.com/repos/symfony/console/zipball/7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3",
+                "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3",
                 "shasum": ""
             },
             "require": {
@@ -28860,7 +28862,7 @@
                 "terminal"
             ],
             "support": {
-                "source": "https://github.com/symfony/console/tree/v6.4.21"
+                "source": "https://github.com/symfony/console/tree/v6.4.22"
             },
             "funding": [
                 {
@@ -28876,11 +28878,11 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-07T15:42:41+00:00"
+            "time": "2025-05-07T07:05:04+00:00"
         },
         {
             "name": "symfony/css-selector",
-            "version": "v7.2.0",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/css-selector.git",
@@ -28925,7 +28927,7 @@
             "description": "Converts CSS selectors to XPath expressions",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/css-selector/tree/v7.2.0"
+                "source": "https://github.com/symfony/css-selector/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -28945,16 +28947,16 @@
         },
         {
             "name": "symfony/dependency-injection",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/dependency-injection.git",
-                "reference": "2ca85496cde37f825bd14f7e3548e2793ca90712"
+                "reference": "f64a8f3fa7d4ad5e85de1b128a0e03faed02b732"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/2ca85496cde37f825bd14f7e3548e2793ca90712",
-                "reference": "2ca85496cde37f825bd14f7e3548e2793ca90712",
+                "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/f64a8f3fa7d4ad5e85de1b128a0e03faed02b732",
+                "reference": "f64a8f3fa7d4ad5e85de1b128a0e03faed02b732",
                 "shasum": ""
             },
             "require": {
@@ -29005,7 +29007,7 @@
             "description": "Allows you to standardize and centralize the way objects are constructed in your application",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/dependency-injection/tree/v7.2.6"
+                "source": "https://github.com/symfony/dependency-injection/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29021,20 +29023,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-27T13:37:55+00:00"
+            "time": "2025-05-19T13:28:56+00:00"
         },
         {
             "name": "symfony/deprecation-contracts",
-            "version": "v3.5.1",
+            "version": "v3.6.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/deprecation-contracts.git",
-                "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"
+                "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6",
-                "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6",
+                "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62",
+                "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62",
                 "shasum": ""
             },
             "require": {
@@ -29047,7 +29049,7 @@
                     "name": "symfony/contracts"
                 },
                 "branch-alias": {
-                    "dev-main": "3.5-dev"
+                    "dev-main": "3.6-dev"
                 }
             },
             "autoload": {
@@ -29072,7 +29074,7 @@
             "description": "A generic function and convention to trigger deprecation notices",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"
+                "source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"
             },
             "funding": [
                 {
@@ -29088,20 +29090,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-09-25T14:20:29+00:00"
+            "time": "2024-09-25T14:21:43+00:00"
         },
         {
             "name": "symfony/error-handler",
-            "version": "v7.2.5",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/error-handler.git",
-                "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b"
+                "reference": "cf68d225bc43629de4ff54778029aee6dc191b83"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/error-handler/zipball/102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b",
-                "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b",
+                "url": "https://api.github.com/repos/symfony/error-handler/zipball/cf68d225bc43629de4ff54778029aee6dc191b83",
+                "reference": "cf68d225bc43629de4ff54778029aee6dc191b83",
                 "shasum": ""
             },
             "require": {
@@ -29114,9 +29116,11 @@
                 "symfony/http-kernel": "<6.4"
             },
             "require-dev": {
+                "symfony/console": "^6.4|^7.0",
                 "symfony/deprecation-contracts": "^2.5|^3",
                 "symfony/http-kernel": "^6.4|^7.0",
-                "symfony/serializer": "^6.4|^7.0"
+                "symfony/serializer": "^6.4|^7.0",
+                "symfony/webpack-encore-bundle": "^1.0|^2.0"
             },
             "bin": [
                 "Resources/bin/patch-type-declarations"
@@ -29147,7 +29151,7 @@
             "description": "Provides tools to manage errors and ease debugging PHP code",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/error-handler/tree/v7.2.5"
+                "source": "https://github.com/symfony/error-handler/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29163,20 +29167,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-03-03T07:12:39+00:00"
+            "time": "2025-05-29T07:19:49+00:00"
         },
         {
             "name": "symfony/event-dispatcher",
-            "version": "v7.2.0",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/event-dispatcher.git",
-                "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"
+                "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1",
-                "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1",
+                "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/497f73ac996a598c92409b44ac43b6690c4f666d",
+                "reference": "497f73ac996a598c92409b44ac43b6690c4f666d",
                 "shasum": ""
             },
             "require": {
@@ -29227,7 +29231,7 @@
             "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"
+                "source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29243,20 +29247,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-09-25T14:21:43+00:00"
+            "time": "2025-04-22T09:11:45+00:00"
         },
         {
             "name": "symfony/event-dispatcher-contracts",
-            "version": "v3.5.1",
+            "version": "v3.6.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/event-dispatcher-contracts.git",
-                "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"
+                "reference": "59eb412e93815df44f05f342958efa9f46b1e586"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f",
-                "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f",
+                "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586",
+                "reference": "59eb412e93815df44f05f342958efa9f46b1e586",
                 "shasum": ""
             },
             "require": {
@@ -29270,7 +29274,7 @@
                     "name": "symfony/contracts"
                 },
                 "branch-alias": {
-                    "dev-main": "3.5-dev"
+                    "dev-main": "3.6-dev"
                 }
             },
             "autoload": {
@@ -29303,7 +29307,7 @@
                 "standards"
             ],
             "support": {
-                "source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"
+                "source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"
             },
             "funding": [
                 {
@@ -29319,11 +29323,11 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-09-25T14:20:29+00:00"
+            "time": "2024-09-25T14:21:43+00:00"
         },
         {
             "name": "symfony/filesystem",
-            "version": "v7.2.0",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/filesystem.git",
@@ -29369,7 +29373,7 @@
             "description": "Provides basic utilities for the filesystem",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/filesystem/tree/v7.2.0"
+                "source": "https://github.com/symfony/filesystem/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29389,16 +29393,16 @@
         },
         {
             "name": "symfony/finder",
-            "version": "v7.2.2",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/finder.git",
-                "reference": "87a71856f2f56e4100373e92529eed3171695cfb"
+                "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb",
-                "reference": "87a71856f2f56e4100373e92529eed3171695cfb",
+                "url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d",
+                "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d",
                 "shasum": ""
             },
             "require": {
@@ -29433,7 +29437,7 @@
             "description": "Finds files and directories via an intuitive fluent interface",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/finder/tree/v7.2.2"
+                "source": "https://github.com/symfony/finder/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29449,20 +29453,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-12-30T19:00:17+00:00"
+            "time": "2024-12-30T19:00:26+00:00"
         },
         {
             "name": "symfony/http-client",
-            "version": "v7.2.4",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/http-client.git",
-                "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6"
+                "reference": "57e4fb86314015a695a750ace358d07a7e37b8a9"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/http-client/zipball/78981a2ffef6437ed92d4d7e2a86a82f256c6dc6",
-                "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6",
+                "url": "https://api.github.com/repos/symfony/http-client/zipball/57e4fb86314015a695a750ace358d07a7e37b8a9",
+                "reference": "57e4fb86314015a695a750ace358d07a7e37b8a9",
                 "shasum": ""
             },
             "require": {
@@ -29528,7 +29532,7 @@
                 "http"
             ],
             "support": {
-                "source": "https://github.com/symfony/http-client/tree/v7.2.4"
+                "source": "https://github.com/symfony/http-client/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29544,20 +29548,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-02-13T10:27:23+00:00"
+            "time": "2025-05-02T08:23:16+00:00"
         },
         {
             "name": "symfony/http-client-contracts",
-            "version": "v3.5.2",
+            "version": "v3.6.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/http-client-contracts.git",
-                "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645"
+                "reference": "75d7043853a42837e68111812f4d964b01e5101c"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ee8d807ab20fcb51267fdace50fbe3494c31e645",
-                "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645",
+                "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/75d7043853a42837e68111812f4d964b01e5101c",
+                "reference": "75d7043853a42837e68111812f4d964b01e5101c",
                 "shasum": ""
             },
             "require": {
@@ -29570,7 +29574,7 @@
                     "name": "symfony/contracts"
                 },
                 "branch-alias": {
-                    "dev-main": "3.5-dev"
+                    "dev-main": "3.6-dev"
                 }
             },
             "autoload": {
@@ -29606,7 +29610,7 @@
                 "standards"
             ],
             "support": {
-                "source": "https://github.com/symfony/http-client-contracts/tree/v3.5.2"
+                "source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0"
             },
             "funding": [
                 {
@@ -29622,20 +29626,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-12-07T08:49:48+00:00"
+            "time": "2025-04-29T11:18:49+00:00"
         },
         {
             "name": "symfony/http-foundation",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/http-foundation.git",
-                "reference": "6023ec7607254c87c5e69fb3558255aca440d72b"
+                "reference": "4236baf01609667d53b20371486228231eb135fd"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/http-foundation/zipball/6023ec7607254c87c5e69fb3558255aca440d72b",
-                "reference": "6023ec7607254c87c5e69fb3558255aca440d72b",
+                "url": "https://api.github.com/repos/symfony/http-foundation/zipball/4236baf01609667d53b20371486228231eb135fd",
+                "reference": "4236baf01609667d53b20371486228231eb135fd",
                 "shasum": ""
             },
             "require": {
@@ -29652,6 +29656,7 @@
                 "doctrine/dbal": "^3.6|^4",
                 "predis/predis": "^1.1|^2.0",
                 "symfony/cache": "^6.4.12|^7.1.5",
+                "symfony/clock": "^6.4|^7.0",
                 "symfony/dependency-injection": "^6.4|^7.0",
                 "symfony/expression-language": "^6.4|^7.0",
                 "symfony/http-kernel": "^6.4|^7.0",
@@ -29684,7 +29689,7 @@
             "description": "Defines an object-oriented layer for the HTTP specification",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/http-foundation/tree/v7.2.6"
+                "source": "https://github.com/symfony/http-foundation/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29700,20 +29705,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-09T08:14:01+00:00"
+            "time": "2025-05-12T14:48:23+00:00"
         },
         {
             "name": "symfony/http-kernel",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/http-kernel.git",
-                "reference": "f9dec01e6094a063e738f8945ef69c0cfcf792ec"
+                "reference": "ac7b8e163e8c83dce3abcc055a502d4486051a9f"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/http-kernel/zipball/f9dec01e6094a063e738f8945ef69c0cfcf792ec",
-                "reference": "f9dec01e6094a063e738f8945ef69c0cfcf792ec",
+                "url": "https://api.github.com/repos/symfony/http-kernel/zipball/ac7b8e163e8c83dce3abcc055a502d4486051a9f",
+                "reference": "ac7b8e163e8c83dce3abcc055a502d4486051a9f",
                 "shasum": ""
             },
             "require": {
@@ -29721,8 +29726,8 @@
                 "psr/log": "^1|^2|^3",
                 "symfony/deprecation-contracts": "^2.5|^3",
                 "symfony/error-handler": "^6.4|^7.0",
-                "symfony/event-dispatcher": "^6.4|^7.0",
-                "symfony/http-foundation": "^6.4|^7.0",
+                "symfony/event-dispatcher": "^7.3",
+                "symfony/http-foundation": "^7.3",
                 "symfony/polyfill-ctype": "^1.8"
             },
             "conflict": {
@@ -29798,7 +29803,7 @@
             "description": "Provides a structured process for converting a Request into a Response",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/http-kernel/tree/v7.2.6"
+                "source": "https://github.com/symfony/http-kernel/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -29814,20 +29819,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-05-02T09:04:03+00:00"
+            "time": "2025-05-29T07:47:32+00:00"
         },
         {
             "name": "symfony/intl",
-            "version": "v6.4.21",
+            "version": "v6.4.22",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/intl.git",
-                "reference": "b248d227fa10fd6345efd4c1c74efaa1c1de6f76"
+                "reference": "aaecb52f18a6f95766a239ca0a6cc0df983d92cc"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/intl/zipball/b248d227fa10fd6345efd4c1c74efaa1c1de6f76",
-                "reference": "b248d227fa10fd6345efd4c1c74efaa1c1de6f76",
+                "url": "https://api.github.com/repos/symfony/intl/zipball/aaecb52f18a6f95766a239ca0a6cc0df983d92cc",
+                "reference": "aaecb52f18a6f95766a239ca0a6cc0df983d92cc",
                 "shasum": ""
             },
             "require": {
@@ -29881,7 +29886,7 @@
                 "localization"
             ],
             "support": {
-                "source": "https://github.com/symfony/intl/tree/v6.4.21"
+                "source": "https://github.com/symfony/intl/tree/v6.4.22"
             },
             "funding": [
                 {
@@ -29897,7 +29902,7 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-07T19:02:30+00:00"
+            "time": "2025-05-04T12:02:38+00:00"
         },
         {
             "name": "symfony/polyfill-ctype",
@@ -30672,16 +30677,16 @@
         },
         {
             "name": "symfony/service-contracts",
-            "version": "v3.5.1",
+            "version": "v3.6.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/service-contracts.git",
-                "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"
+                "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0",
-                "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0",
+                "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4",
+                "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4",
                 "shasum": ""
             },
             "require": {
@@ -30699,7 +30704,7 @@
                     "name": "symfony/contracts"
                 },
                 "branch-alias": {
-                    "dev-main": "3.5-dev"
+                    "dev-main": "3.6-dev"
                 }
             },
             "autoload": {
@@ -30735,7 +30740,7 @@
                 "standards"
             ],
             "support": {
-                "source": "https://github.com/symfony/service-contracts/tree/v3.5.1"
+                "source": "https://github.com/symfony/service-contracts/tree/v3.6.0"
             },
             "funding": [
                 {
@@ -30751,7 +30756,7 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-09-25T14:20:29+00:00"
+            "time": "2025-04-25T09:37:31+00:00"
         },
         {
             "name": "symfony/string",
@@ -30841,20 +30846,21 @@
         },
         {
             "name": "symfony/var-dumper",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/var-dumper.git",
-                "reference": "9c46038cd4ed68952166cf7001b54eb539184ccb"
+                "reference": "548f6760c54197b1084e1e5c71f6d9d523f2f78e"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/var-dumper/zipball/9c46038cd4ed68952166cf7001b54eb539184ccb",
-                "reference": "9c46038cd4ed68952166cf7001b54eb539184ccb",
+                "url": "https://api.github.com/repos/symfony/var-dumper/zipball/548f6760c54197b1084e1e5c71f6d9d523f2f78e",
+                "reference": "548f6760c54197b1084e1e5c71f6d9d523f2f78e",
                 "shasum": ""
             },
             "require": {
                 "php": ">=8.2",
+                "symfony/deprecation-contracts": "^2.5|^3",
                 "symfony/polyfill-mbstring": "~1.0"
             },
             "conflict": {
@@ -30904,7 +30910,7 @@
                 "dump"
             ],
             "support": {
-                "source": "https://github.com/symfony/var-dumper/tree/v7.2.6"
+                "source": "https://github.com/symfony/var-dumper/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -30920,24 +30926,25 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-09T08:14:01+00:00"
+            "time": "2025-04-27T18:39:23+00:00"
         },
         {
             "name": "symfony/var-exporter",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/var-exporter.git",
-                "reference": "422b8de94c738830a1e071f59ad14d67417d7007"
+                "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/var-exporter/zipball/422b8de94c738830a1e071f59ad14d67417d7007",
-                "reference": "422b8de94c738830a1e071f59ad14d67417d7007",
+                "url": "https://api.github.com/repos/symfony/var-exporter/zipball/c9a1168891b5aaadfd6332ef44393330b3498c4c",
+                "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c",
                 "shasum": ""
             },
             "require": {
-                "php": ">=8.2"
+                "php": ">=8.2",
+                "symfony/deprecation-contracts": "^2.5|^3"
             },
             "require-dev": {
                 "symfony/property-access": "^6.4|^7.0",
@@ -30980,7 +30987,7 @@
                 "serialize"
             ],
             "support": {
-                "source": "https://github.com/symfony/var-exporter/tree/v7.2.6"
+                "source": "https://github.com/symfony/var-exporter/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -30996,20 +31003,20 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-05-02T08:36:00+00:00"
+            "time": "2025-05-15T09:04:05+00:00"
         },
         {
             "name": "symfony/yaml",
-            "version": "v7.2.6",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/yaml.git",
-                "reference": "0feafffb843860624ddfd13478f481f4c3cd8b23"
+                "reference": "cea40a48279d58dc3efee8112634cb90141156c2"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/yaml/zipball/0feafffb843860624ddfd13478f481f4c3cd8b23",
-                "reference": "0feafffb843860624ddfd13478f481f4c3cd8b23",
+                "url": "https://api.github.com/repos/symfony/yaml/zipball/cea40a48279d58dc3efee8112634cb90141156c2",
+                "reference": "cea40a48279d58dc3efee8112634cb90141156c2",
                 "shasum": ""
             },
             "require": {
@@ -31052,7 +31059,7 @@
             "description": "Loads and dumps YAML files",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/yaml/tree/v7.2.6"
+                "source": "https://github.com/symfony/yaml/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -31068,7 +31075,7 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2025-04-04T10:10:11+00:00"
+            "time": "2025-04-04T10:10:33+00:00"
         },
         {
             "name": "tedivm/jshrink",
@@ -33185,16 +33192,16 @@
         },
         {
             "name": "phpstan/phpstan",
-            "version": "1.12.26",
+            "version": "1.12.27",
             "source": {
                 "type": "git",
                 "url": "https://github.com/phpstan/phpstan.git",
-                "reference": "84cbf8f018e01834b9b1ac3dacf3b9780e209e53"
+                "reference": "3a6e423c076ab39dfedc307e2ac627ef579db162"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/phpstan/phpstan/zipball/84cbf8f018e01834b9b1ac3dacf3b9780e209e53",
-                "reference": "84cbf8f018e01834b9b1ac3dacf3b9780e209e53",
+                "url": "https://api.github.com/repos/phpstan/phpstan/zipball/3a6e423c076ab39dfedc307e2ac627ef579db162",
+                "reference": "3a6e423c076ab39dfedc307e2ac627ef579db162",
                 "shasum": ""
             },
             "require": {
@@ -33239,7 +33246,7 @@
                     "type": "github"
                 }
             ],
-            "time": "2025-05-14T11:08:32+00:00"
+            "time": "2025-05-21T20:51:45+00:00"
         },
         {
             "name": "phpunit/php-code-coverage",
@@ -35335,16 +35342,16 @@
         },
         {
             "name": "symfony/options-resolver",
-            "version": "v7.2.0",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/options-resolver.git",
-                "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50"
+                "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca"
             },
             "dist": {
                 "type": "zip",
-                "url": "https://api.github.com/repos/symfony/options-resolver/zipball/7da8fbac9dcfef75ffc212235d76b2754ce0cf50",
-                "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50",
+                "url": "https://api.github.com/repos/symfony/options-resolver/zipball/afb9a8038025e5dbc657378bfab9198d75f10fca",
+                "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca",
                 "shasum": ""
             },
             "require": {
@@ -35382,7 +35389,7 @@
                 "options"
             ],
             "support": {
-                "source": "https://github.com/symfony/options-resolver/tree/v7.2.0"
+                "source": "https://github.com/symfony/options-resolver/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -35398,11 +35405,11 @@
                     "type": "tidelift"
                 }
             ],
-            "time": "2024-11-20T11:17:29+00:00"
+            "time": "2025-04-04T13:12:05+00:00"
         },
         {
             "name": "symfony/stopwatch",
-            "version": "v7.2.4",
+            "version": "v7.3.0",
             "source": {
                 "type": "git",
                 "url": "https://github.com/symfony/stopwatch.git",
@@ -35444,7 +35451,7 @@
             "description": "Provides a way to profile code",
             "homepage": "https://symfony.com",
             "support": {
-                "source": "https://github.com/symfony/stopwatch/tree/v7.2.4"
+                "source": "https://github.com/symfony/stopwatch/tree/v7.3.0"
             },
             "funding": [
                 {
@@ -35563,12 +35570,12 @@
     ],
     "aliases": [],
     "minimum-stability": "stable",
-    "stability-flags": [],
+    "stability-flags": {},
     "prefer-stable": true,
     "prefer-lowest": false,
     "platform": {
         "ext-curl": "*"
     },
-    "platform-dev": [],
+    "platform-dev": {},
     "plugin-api-version": "2.6.0"
 }
